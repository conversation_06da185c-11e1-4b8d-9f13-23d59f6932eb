<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能透明玻璃球 - 深海效果</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            min-height: 100vh;
            background: linear-gradient(180deg, 
                #001122 0%, 
                #003366 30%, 
                #004488 60%, 
                #0066aa 100%);
            overflow: hidden;
            position: relative;
            font-family: 'Arial', sans-serif;
        }

        .ocean-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(ellipse at center, 
                rgba(0, 100, 200, 0.1) 0%, 
                rgba(0, 50, 150, 0.3) 50%, 
                rgba(0, 20, 80, 0.6) 100%);
            animation: oceanFlow 20s ease-in-out infinite;
        }

        .water-ripples {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(255, 255, 255, 0.04) 0%, transparent 50%);
            animation: rippleMove 15s linear infinite;
        }

        .glass-sphere {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 300px;
            height: 300px;
            transform: translate(-50%, -50%);
            border-radius: 50%;
            background: radial-gradient(circle at 30% 30%, 
                rgba(255, 255, 255, 0.2) 0%,
                rgba(200, 230, 255, 0.1) 30%,
                rgba(100, 180, 255, 0.05) 60%,
                rgba(0, 100, 200, 0.1) 100%);
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 
                0 0 50px rgba(100, 200, 255, 0.3),
                inset 0 0 50px rgba(255, 255, 255, 0.1),
                0 20px 40px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            cursor: pointer;
            transition: all 0.3s ease;
            animation: sphereFloat 8s ease-in-out infinite;
        }

        .glass-sphere:hover {
            transform: translate(-50%, -50%) scale(1.05);
            box-shadow: 
                0 0 80px rgba(100, 200, 255, 0.5),
                inset 0 0 80px rgba(255, 255, 255, 0.2),
                0 30px 60px rgba(0, 0, 0, 0.4);
        }

        .sphere-inner {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 80%;
            height: 80%;
            transform: translate(-50%, -50%);
            border-radius: 50%;
            background: radial-gradient(circle at 40% 40%, 
                rgba(255, 255, 255, 0.3) 0%,
                rgba(150, 200, 255, 0.2) 50%,
                transparent 100%);
            animation: innerGlow 6s ease-in-out infinite alternate;
        }

        .particles-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .particle {
            position: absolute;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.8), rgba(100, 200, 255, 0.3));
            border-radius: 50%;
            pointer-events: none;
        }

        .bubble {
            position: absolute;
            border-radius: 50%;
            background: radial-gradient(circle at 30% 30%, 
                rgba(255, 255, 255, 0.4), 
                rgba(200, 230, 255, 0.2), 
                rgba(100, 180, 255, 0.1));
            border: 1px solid rgba(255, 255, 255, 0.3);
            animation: bubbleRise linear infinite;
        }

        .whale {
            position: absolute;
            width: 200px;
            height: 80px;
            opacity: 0.6;
            animation: whaleSwim 25s linear infinite;
        }

        .whale svg {
            width: 100%;
            height: 100%;
            filter: drop-shadow(0 0 20px rgba(100, 200, 255, 0.5));
        }

        @keyframes oceanFlow {
            0%, 100% { transform: scale(1) rotate(0deg); }
            50% { transform: scale(1.1) rotate(2deg); }
        }

        @keyframes rippleMove {
            0% { transform: translateX(0) translateY(0); }
            25% { transform: translateX(-20px) translateY(-10px); }
            50% { transform: translateX(20px) translateY(-20px); }
            75% { transform: translateX(-10px) translateY(-15px); }
            100% { transform: translateX(0) translateY(0); }
        }

        @keyframes sphereFloat {
            0%, 100% { transform: translate(-50%, -50%) translateY(0px); }
            50% { transform: translate(-50%, -50%) translateY(-10px); }
        }

        @keyframes innerGlow {
            0% { opacity: 0.6; transform: translate(-50%, -50%) scale(1); }
            100% { opacity: 1; transform: translate(-50%, -50%) scale(1.1); }
        }

        @keyframes bubbleRise {
            0% {
                transform: translateY(100vh) scale(0);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) scale(1);
                opacity: 0;
            }
        }

        @keyframes whaleSwim {
            0% {
                transform: translateX(-250px) translateY(20vh) scaleX(1);
            }
            25% {
                transform: translateX(25vw) translateY(30vh) scaleX(1);
            }
            50% {
                transform: translateX(75vw) translateY(60vh) scaleX(-1);
            }
            75% {
                transform: translateX(50vw) translateY(40vh) scaleX(-1);
            }
            100% {
                transform: translateX(-250px) translateY(20vh) scaleX(1);
            }
        }

        .info {
            position: absolute;
            top: 30px;
            left: 50%;
            transform: translateX(-50%);
            color: rgba(255, 255, 255, 0.8);
            text-align: center;
            font-size: 16px;
            text-shadow: 0 0 10px rgba(100, 200, 255, 0.5);
            z-index: 100;
        }

        .controls {
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            color: rgba(255, 255, 255, 0.7);
            text-align: center;
            font-size: 14px;
            z-index: 100;
        }
    </style>
</head>
<body>
    <div class="ocean-overlay"></div>
    <div class="water-ripples"></div>
    
    <div class="info">
        智能透明玻璃球 - 深海环境
    </div>
    
    <div class="glass-sphere" id="glassSphere">
        <div class="sphere-inner"></div>
    </div>
    
    <div class="particles-container" id="particlesContainer"></div>
    
    <div class="whale">
        <svg viewBox="0 0 200 80" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="whaleGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:rgba(100,200,255,0.6);stop-opacity:1" />
                    <stop offset="50%" style="stop-color:rgba(150,220,255,0.4);stop-opacity:1" />
                    <stop offset="100%" style="stop-color:rgba(200,240,255,0.2);stop-opacity:1" />
                </linearGradient>
            </defs>
            <path d="M20,40 Q60,20 120,35 Q160,45 180,40 Q190,45 185,50 Q160,55 120,45 Q60,60 20,40 Z" 
                  fill="url(#whaleGradient)" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
            <ellipse cx="40" cy="35" rx="3" ry="2" fill="rgba(255,255,255,0.8)"/>
            <path d="M180,40 Q195,35 190,50 Q185,45 180,40" fill="url(#whaleGradient)"/>
        </svg>
    </div>
    
    <div class="controls">
        点击玻璃球体验交互效果 | 鼠标移动查看粒子跟随
    </div>

    <script>
        class OceanEffect {
            constructor() {
                this.particles = [];
                this.bubbles = [];
                this.mouseX = 0;
                this.mouseY = 0;
                this.init();
            }

            init() {
                this.createParticles();
                this.createBubbles();
                this.bindEvents();
                this.animate();
            }

            createParticles() {
                const container = document.getElementById('particlesContainer');
                for (let i = 0; i < 50; i++) {
                    const particle = document.createElement('div');
                    particle.className = 'particle';
                    
                    const size = Math.random() * 3 + 1;
                    particle.style.width = size + 'px';
                    particle.style.height = size + 'px';
                    particle.style.left = Math.random() * window.innerWidth + 'px';
                    particle.style.top = Math.random() * window.innerHeight + 'px';
                    
                    container.appendChild(particle);
                    
                    this.particles.push({
                        element: particle,
                        x: Math.random() * window.innerWidth,
                        y: Math.random() * window.innerHeight,
                        vx: (Math.random() - 0.5) * 0.5,
                        vy: (Math.random() - 0.5) * 0.5,
                        size: size
                    });
                }
            }

            createBubbles() {
                setInterval(() => {
                    if (this.bubbles.length < 10) {
                        this.createBubble();
                    }
                }, 2000);
            }

            createBubble() {
                const bubble = document.createElement('div');
                bubble.className = 'bubble';
                
                const size = Math.random() * 20 + 10;
                bubble.style.width = size + 'px';
                bubble.style.height = size + 'px';
                bubble.style.left = Math.random() * window.innerWidth + 'px';
                bubble.style.animationDuration = (Math.random() * 5 + 8) + 's';
                
                document.body.appendChild(bubble);
                
                setTimeout(() => {
                    if (bubble.parentNode) {
                        bubble.parentNode.removeChild(bubble);
                    }
                }, 13000);
            }

            bindEvents() {
                document.addEventListener('mousemove', (e) => {
                    this.mouseX = e.clientX;
                    this.mouseY = e.clientY;
                });

                document.getElementById('glassSphere').addEventListener('click', (e) => {
                    this.createClickEffect(e.clientX, e.clientY);
                });
            }

            createClickEffect(x, y) {
                for (let i = 0; i < 10; i++) {
                    const particle = document.createElement('div');
                    particle.className = 'particle';
                    particle.style.width = '4px';
                    particle.style.height = '4px';
                    particle.style.left = x + 'px';
                    particle.style.top = y + 'px';
                    particle.style.background = 'radial-gradient(circle, rgba(255,255,255,1), rgba(100,200,255,0.5))';
                    
                    document.getElementById('particlesContainer').appendChild(particle);
                    
                    const angle = (Math.PI * 2 * i) / 10;
                    const velocity = Math.random() * 100 + 50;
                    const vx = Math.cos(angle) * velocity;
                    const vy = Math.sin(angle) * velocity;
                    
                    let posX = x;
                    let posY = y;
                    let opacity = 1;
                    
                    const animate = () => {
                        posX += vx * 0.02;
                        posY += vy * 0.02;
                        opacity -= 0.02;
                        
                        particle.style.left = posX + 'px';
                        particle.style.top = posY + 'px';
                        particle.style.opacity = opacity;
                        
                        if (opacity > 0) {
                            requestAnimationFrame(animate);
                        } else {
                            particle.remove();
                        }
                    };
                    
                    animate();
                }
            }

            animate() {
                this.particles.forEach(particle => {
                    // 粒子向鼠标位置移动
                    const dx = this.mouseX - particle.x;
                    const dy = this.mouseY - particle.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);
                    
                    if (distance < 100) {
                        particle.vx += dx * 0.0001;
                        particle.vy += dy * 0.0001;
                    }
                    
                    particle.x += particle.vx;
                    particle.y += particle.vy;
                    
                    // 边界检测
                    if (particle.x < 0 || particle.x > window.innerWidth) particle.vx *= -1;
                    if (particle.y < 0 || particle.y > window.innerHeight) particle.vy *= -1;
                    
                    particle.x = Math.max(0, Math.min(window.innerWidth, particle.x));
                    particle.y = Math.max(0, Math.min(window.innerHeight, particle.y));
                    
                    particle.element.style.left = particle.x + 'px';
                    particle.element.style.top = particle.y + 'px';
                });
                
                requestAnimationFrame(() => this.animate());
            }
        }

        // 初始化海洋效果
        window.addEventListener('load', () => {
            new OceanEffect();
        });

        // 响应式处理
        window.addEventListener('resize', () => {
            location.reload();
        });
    </script>
</body>
</html>
