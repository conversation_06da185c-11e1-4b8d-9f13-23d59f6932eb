<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI分拣系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: auto;
            padding: 20px;
        }

        .container {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            max-width: 1600px;
            margin: 0 auto;
        }

        .phone-frame {
            width: 375px;
            height: 812px;
            border: 1px solid #ccc;
            border-radius: 25px;
            overflow: hidden;
            position: relative;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .glass-effect {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .header {
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(15px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .header h1 {
            color: white;
            font-size: 18px;
            font-weight: 600;
        }

        .content {
            height: calc(100% - 120px);
            padding: 20px;
            overflow-y: auto;
        }

        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(15px);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: space-around;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: white;
            font-size: 12px;
        }

        .ai-button {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            display: flex;
            align-items: center;
            justify-content: center;
            animation: pulse 2s infinite;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .list-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 12px;
            margin-bottom: 8px;
            border-left: 4px solid #4ecdc4;
        }

        .list-item.completed {
            border-left-color: #2ecc71;
            background: rgba(46, 204, 113, 0.2);
        }

        .grid-layout {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            margin: 15px 0;
        }

        .product-item {
            aspect-ratio: 1;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
            text-align: center;
        }

        .chart-container {
            height: 200px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            margin: 15px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .landscape {
            width: 812px;
            height: 375px;
        }

        .landscape .content {
            display: flex;
            height: calc(100% - 60px);
        }

        .sidebar {
            width: 200px;
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
        }

        .main-area {
            flex: 1;
            padding: 15px;
        }

        .right-panel {
            width: 200px;
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin: 15px 0;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            padding: 15px;
            text-align: center;
            color: white;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .permission-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            margin-bottom: 8px;
        }

        .toggle {
            width: 40px;
            height: 20px;
            background: #ccc;
            border-radius: 10px;
            position: relative;
            cursor: pointer;
        }

        .toggle.active {
            background: #4ecdc4;
        }

        .toggle::after {
            content: '';
            width: 16px;
            height: 16px;
            background: white;
            border-radius: 50%;
            position: absolute;
            top: 2px;
            left: 2px;
            transition: 0.3s;
        }

        .toggle.active::after {
            left: 22px;
        }

        .text-white { color: white; }
        .text-sm { font-size: 14px; }
        .text-xs { font-size: 12px; }
        .mb-2 { margin-bottom: 8px; }
        .mb-3 { margin-bottom: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <!-- 1. 入库管理 - 入库列表 -->
        <div class="phone-frame">
            <div class="header">
                <h1>入库管理</h1>
            </div>
            <div class="content">
                <div class="card">
                    <h3 class="text-white mb-2">待入库订单</h3>
                    <div class="list-item">
                        <div class="text-white text-sm">订单 #001</div>
                        <div class="text-white text-xs">小白菜 5斤 | 大白菜 6斤 | 蘑菇 1箱</div>
                    </div>
                    <div class="list-item">
                        <div class="text-white text-sm">订单 #002</div>
                        <div class="text-white text-xs">西红柿 3斤 | 黄瓜 4斤 | 土豆 2斤</div>
                    </div>
                </div>
                
                <div class="card">
                    <h3 class="text-white mb-2">已入库订单</h3>
                    <div class="list-item completed">
                        <div class="text-white text-sm">订单 #003</div>
                        <div class="text-white text-xs">胡萝卜 2斤 | 白萝卜 3斤</div>
                    </div>
                </div>
            </div>
            <div class="bottom-nav">
                <div class="nav-item">
                    <div>📦</div>
                    <div>功能</div>
                </div>
                <div class="nav-item">
                    <div class="ai-button">🎤</div>
                    <div>AI</div>
                </div>
                <div class="nav-item">
                    <div>👤</div>
                    <div>我的</div>
                </div>
            </div>
        </div>

        <!-- 2. 入库详细 -->
        <div class="phone-frame">
            <div class="header">
                <h1>入库详细 - 订单#001</h1>
            </div>
            <div class="content">
                <div class="card">
                    <h3 class="text-white mb-3">商品清单</h3>
                    <div class="list-item completed">
                        <div class="text-white text-sm">✅ 小白菜</div>
                        <div class="text-white text-xs">数量: 5斤 | 已入库</div>
                    </div>
                    <div class="list-item completed">
                        <div class="text-white text-sm">✅ 大白菜</div>
                        <div class="text-white text-xs">数量: 6斤 | 已入库</div>
                    </div>
                    <div class="list-item">
                        <div class="text-white text-sm">⏳ 蘑菇</div>
                        <div class="text-white text-xs">数量: 1箱 | 待入库</div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="text-white text-sm">入库进度: 2/3 完成</div>
                    <div style="background: rgba(255,255,255,0.2); height: 8px; border-radius: 4px; margin-top: 8px;">
                        <div style="background: #2ecc71; height: 100%; width: 66%; border-radius: 4px;"></div>
                    </div>
                </div>
            </div>
            <div class="bottom-nav">
                <div class="nav-item">
                    <div>📦</div>
                    <div>功能</div>
                </div>
                <div class="nav-item">
                    <div class="ai-button">🎤</div>
                    <div>AI</div>
                </div>
                <div class="nav-item">
                    <div>👤</div>
                    <div>我的</div>
                </div>
            </div>
        </div>

        <!-- 3. 录单功能 -->
        <div class="phone-frame">
            <div class="header">
                <h1>录单功能</h1>
            </div>
            <div class="content">
                <div class="card">
                    <h3 class="text-white mb-2">客户订单录入</h3>
                    <div class="list-item">
                        <div class="text-white text-sm">小白菜</div>
                        <div class="text-white text-xs">5斤 - ¥15.00</div>
                    </div>
                    <div class="list-item">
                        <div class="text-white text-sm">大白菜</div>
                        <div class="text-white text-xs">6斤 - ¥18.00</div>
                    </div>
                    <div class="list-item">
                        <div class="text-white text-sm">蘑菇</div>
                        <div class="text-white text-xs">1箱 - ¥25.00</div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="text-white text-sm mb-2">订单总计</div>
                    <div class="text-white" style="font-size: 20px; font-weight: bold;">¥58.00</div>
                </div>
                
                <button style="width: 100%; padding: 15px; background: linear-gradient(135deg, #4ecdc4, #44a08d); border: none; border-radius: 10px; color: white; font-size: 16px; font-weight: bold;">
                    确认录单
                </button>
            </div>
            <div class="bottom-nav">
                <div class="nav-item">
                    <div>📦</div>
                    <div>功能</div>
                </div>
                <div class="nav-item">
                    <div class="ai-button">🎤</div>
                    <div>AI</div>
                </div>
                <div class="nav-item">
                    <div>👤</div>
                    <div>我的</div>
                </div>
            </div>
        </div>

        <!-- 4. 分拣系统 (横屏) -->
        <div class="phone-frame landscape">
            <div class="header">
                <h1>分拣系统</h1>
            </div>
            <div class="content">
                <div class="sidebar">
                    <h3 class="text-white mb-3">商品分类</h3>
                    <div class="list-item mb-2">叶菜类</div>
                    <div class="list-item mb-2">根茎类</div>
                    <div class="list-item mb-2">菌菇类</div>
                    <div class="list-item mb-2">果实类</div>
                </div>
                
                <div class="main-area">
                    <h3 class="text-white mb-3">商品选择</h3>
                    <div class="grid-layout">
                        <div class="product-item">🥬<br>小白菜</div>
                        <div class="product-item">🥒<br>黄瓜</div>
                        <div class="product-item">🍅<br>西红柿</div>
                        <div class="product-item">🥕<br>胡萝卜</div>
                        <div class="product-item">🥔<br>土豆</div>
                        <div class="product-item">🍄<br>蘑菇</div>
                        <div class="product-item">🧅<br>洋葱</div>
                        <div class="product-item">🌶️<br>辣椒</div>
                        <div class="product-item">🥦<br>西兰花</div>
                        <div class="product-item">🥬<br>大白菜</div>
                        <div class="product-item">🥬<br>菠菜</div>
                        <div class="product-item">🥒<br>丝瓜</div>
                    </div>
                </div>
                
                <div class="right-panel">
                    <h3 class="text-white mb-3">已分拣列表</h3>
                    <div class="list-item mb-2">
                        <div class="text-xs">小白菜 x2</div>
                    </div>
                    <div class="list-item mb-2">
                        <div class="text-xs">蘑菇 x1</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 5. 财务管理 -->
        <div class="phone-frame">
            <div class="header">
                <h1>财务管理</h1>
            </div>
            <div class="content">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">¥12,580</div>
                        <div class="text-sm">今日收益</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">¥3,240</div>
                        <div class="text-sm">待收款</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">¥8,960</div>
                        <div class="text-sm">本月支出</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">¥45,680</div>
                        <div class="text-sm">本月收益</div>
                    </div>
                </div>

                <div class="chart-container">
                    <div class="text-white">📊 收益趋势图</div>
                </div>

                <div class="card">
                    <h3 class="text-white mb-2">欠款管理</h3>
                    <div class="list-item">
                        <div class="text-white text-sm">张三 - ¥580</div>
                        <div class="text-white text-xs">逾期3天</div>
                    </div>
                    <div class="list-item">
                        <div class="text-white text-sm">李四 - ¥320</div>
                        <div class="text-white text-xs">逾期1天</div>
                    </div>
                </div>
            </div>
            <div class="bottom-nav">
                <div class="nav-item">
                    <div>📦</div>
                    <div>功能</div>
                </div>
                <div class="nav-item">
                    <div class="ai-button">🎤</div>
                    <div>AI</div>
                </div>
                <div class="nav-item">
                    <div>👤</div>
                    <div>我的</div>
                </div>
            </div>
        </div>

        <!-- 6. 快速售卖 (横屏) -->
        <div class="phone-frame landscape">
            <div class="header">
                <h1>快速售卖</h1>
            </div>
            <div class="content">
                <div class="sidebar">
                    <h3 class="text-white mb-3">商品分类</h3>
                    <div class="list-item mb-2">叶菜类</div>
                    <div class="list-item mb-2">根茎类</div>
                    <div class="list-item mb-2">菌菇类</div>
                    <div class="list-item mb-2">果实类</div>
                    <div class="list-item mb-2">调料类</div>
                </div>

                <div class="main-area">
                    <h3 class="text-white mb-3">现货商品</h3>
                    <div class="grid-layout">
                        <div class="product-item">🥬<br>小白菜<br>¥3/斤</div>
                        <div class="product-item">🥒<br>黄瓜<br>¥4/斤</div>
                        <div class="product-item">🍅<br>西红柿<br>¥5/斤</div>
                        <div class="product-item">🥕<br>胡萝卜<br>¥3/斤</div>
                        <div class="product-item">🥔<br>土豆<br>¥2/斤</div>
                        <div class="product-item">🍄<br>蘑菇<br>¥8/斤</div>
                        <div class="product-item">🧅<br>洋葱<br>¥3/斤</div>
                        <div class="product-item">🌶️<br>辣椒<br>¥6/斤</div>
                        <div class="product-item">🥦<br>西兰花<br>¥7/斤</div>
                        <div class="product-item">🥬<br>大白菜<br>¥2/斤</div>
                        <div class="product-item">🥬<br>菠菜<br>¥4/斤</div>
                        <div class="product-item">🥒<br>丝瓜<br>¥5/斤</div>
                    </div>
                </div>

                <div class="right-panel">
                    <h3 class="text-white mb-3">购物车</h3>
                    <div class="list-item mb-2">
                        <div class="text-xs">小白菜 2斤 ¥6</div>
                    </div>
                    <div class="list-item mb-2">
                        <div class="text-xs">蘑菇 1斤 ¥8</div>
                    </div>
                    <div class="card mt-3">
                        <div class="text-white text-sm">总计: ¥14</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 7. 中控台 -->
        <div class="phone-frame">
            <div class="header">
                <h1>中控台</h1>
            </div>
            <div class="content">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">156</div>
                        <div class="text-sm">今日订单</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">89</div>
                        <div class="text-sm">已分拣</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">67</div>
                        <div class="text-sm">分拣中</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">12</div>
                        <div class="text-sm">在线人数</div>
                    </div>
                </div>

                <div class="card">
                    <h3 class="text-white mb-2">实时状态</h3>
                    <div class="list-item">
                        <div class="text-white text-sm">🟢 分拣员A - 工作中</div>
                    </div>
                    <div class="list-item">
                        <div class="text-white text-sm">🟢 分拣员B - 工作中</div>
                    </div>
                    <div class="list-item">
                        <div class="text-white text-sm">🟡 分拣员C - 休息中</div>
                    </div>
                </div>

                <div class="chart-container">
                    <div class="text-white">📈 实时数据监控</div>
                </div>
            </div>
            <div class="bottom-nav">
                <div class="nav-item">
                    <div>📦</div>
                    <div>功能</div>
                </div>
                <div class="nav-item">
                    <div class="ai-button">🎤</div>
                    <div>AI</div>
                </div>
                <div class="nav-item">
                    <div>👤</div>
                    <div>我的</div>
                </div>
            </div>
        </div>

        <!-- 8. 会员中心 -->
        <div class="phone-frame">
            <div class="header">
                <h1>会员中心</h1>
            </div>
            <div class="content">
                <div class="card">
                    <div class="text-white text-sm mb-2">管理员</div>
                    <div class="text-white" style="font-size: 18px; font-weight: bold;">张经理</div>
                </div>

                <div class="card">
                    <h3 class="text-white mb-3">功能模块</h3>
                    <div class="grid-layout">
                        <div class="product-item">📦<br>入库管理</div>
                        <div class="product-item">📝<br>录单功能</div>
                        <div class="product-item">🔄<br>分拣系统</div>
                        <div class="product-item">💰<br>财务管理</div>
                        <div class="product-item">🛒<br>快速售卖</div>
                        <div class="product-item">📊<br>中控台</div>
                        <div class="product-item">👥<br>子账号</div>
                        <div class="product-item">⚙️<br>设置</div>
                    </div>
                </div>

                <button style="width: 100%; padding: 15px; background: linear-gradient(135deg, #667eea, #764ba2); border: none; border-radius: 10px; color: white; font-size: 16px; font-weight: bold; margin-top: 15px;">
                    添加子账号
                </button>
            </div>
            <div class="bottom-nav">
                <div class="nav-item">
                    <div>📦</div>
                    <div>功能</div>
                </div>
                <div class="nav-item">
                    <div class="ai-button">🎤</div>
                    <div>AI</div>
                </div>
                <div class="nav-item">
                    <div>👤</div>
                    <div>我的</div>
                </div>
            </div>
        </div>

        <!-- 9. 添加子账号 -->
        <div class="phone-frame">
            <div class="header">
                <h1>添加子账号</h1>
            </div>
            <div class="content">
                <div class="card">
                    <h3 class="text-white mb-3">账号信息</h3>
                    <div style="margin-bottom: 15px;">
                        <input type="text" placeholder="用户名" style="width: 100%; padding: 12px; border: none; border-radius: 8px; background: rgba(255,255,255,0.2); color: white;">
                    </div>
                    <div style="margin-bottom: 15px;">
                        <input type="password" placeholder="密码" style="width: 100%; padding: 12px; border: none; border-radius: 8px; background: rgba(255,255,255,0.2); color: white;">
                    </div>
                </div>

                <div class="card">
                    <h3 class="text-white mb-3">权限分配</h3>
                    <div class="permission-item">
                        <span class="text-white">入库管理</span>
                        <div class="toggle active"></div>
                    </div>
                    <div class="permission-item">
                        <span class="text-white">录单功能</span>
                        <div class="toggle active"></div>
                    </div>
                    <div class="permission-item">
                        <span class="text-white">分拣系统</span>
                        <div class="toggle"></div>
                    </div>
                    <div class="permission-item">
                        <span class="text-white">财务管理</span>
                        <div class="toggle"></div>
                    </div>
                    <div class="permission-item">
                        <span class="text-white">快速售卖</span>
                        <div class="toggle active"></div>
                    </div>
                </div>

                <div class="card">
                    <h3 class="text-white mb-2">现有子账号</h3>
                    <div class="list-item">
                        <div class="text-white text-sm">员工A - 分拣员</div>
                        <div class="text-white text-xs">权限: 分拣系统</div>
                    </div>
                    <div class="list-item">
                        <div class="text-white text-sm">员工B - 录单员</div>
                        <div class="text-white text-xs">权限: 录单功能, 快速售卖</div>
                    </div>
                </div>

                <button style="width: 100%; padding: 15px; background: linear-gradient(135deg, #4ecdc4, #44a08d); border: none; border-radius: 10px; color: white; font-size: 16px; font-weight: bold;">
                    创建账号
                </button>
            </div>
            <div class="bottom-nav">
                <div class="nav-item">
                    <div>📦</div>
                    <div>功能</div>
                </div>
                <div class="nav-item">
                    <div class="ai-button">🎤</div>
                    <div>AI</div>
                </div>
                <div class="nav-item">
                    <div>👤</div>
                    <div>我的</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 添加交互效果
        document.querySelectorAll('.toggle').forEach(toggle => {
            toggle.addEventListener('click', function() {
                this.classList.toggle('active');
            });
        });

        // 添加产品点击效果
        document.querySelectorAll('.product-item').forEach(item => {
            item.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
            });
        });

        // AI按钮动画
        document.querySelectorAll('.ai-button').forEach(button => {
            button.addEventListener('click', function() {
                this.style.animation = 'none';
                setTimeout(() => {
                    this.style.animation = 'pulse 2s infinite';
                }, 100);
            });
        });
    </script>
</body>
</html>
